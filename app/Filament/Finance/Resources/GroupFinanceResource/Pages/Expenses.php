<?php

namespace App\Filament\Finance\Resources\GroupFinanceResource\Pages;

use App\Enums\ExpenseGroup;
use App\Filament\Finance\Resources\GroupFinanceResource;
use App\Models\Bill;
use App\Models\Currency;
use App\Models\Finance\CashCategory;
use App\Models\Finance\UserCash;
use CodeWithDennis\FilamentSelectTree\SelectTree;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Pages\ManageRelatedRecords;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class Expenses extends ManageRelatedRecords
{
    protected static string $resource = GroupFinanceResource::class;

    protected static string $relationship = 'user_cashes';

    protected static ?string $navigationLabel = 'Expenses';

    protected static ?string $navigationIcon = 'heroicon-o-banknotes';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('user_id')
                    ->label('User')
                    ->relationship('user', 'name')
                    ->searchable()
                    ->preload()
                    ->required(),
                Forms\Components\DateTimePicker::make('cashed_at')
                    ->label('Date')
                    ->default(now())
                    ->required(),
                SelectTree::make('category_id')
                    ->label('Category')
                    ->relationship('category', 'name', 'parent_id')
                    ->searchable()
                    ->live()
                    ->afterStateUpdated(function ($state, $set) {
                        if (CashCategory::find($state)?->group == ExpenseGroup::VendorPayment) {
                            $set('related_type', 'bill');
                        } else {
                            $set('related_type', null);
                        }
                    })
                    ->required(fn ($get) => $get('type') == 'c'),
                Forms\Components\Hidden::make('related_type'),
                Forms\Components\Select::make('related_id')
                    ->label('Bill No.')
                    ->options(fn ($get) => Bill::query()
                        ->orderByDesc('bill_number')
                        ->pluck('bill_number', 'id')
                    )
                    ->searchable()
                    ->preload()
                    ->visible(fn ($get) => $get('related_type') == 'bill'),
                Forms\Components\TextInput::make('details')
                    ->autocomplete('off')
                    ->maxLength(255)
                    ->required(),
                Forms\Components\Radio::make('type')
                    ->options([
                        'd' => 'In',
                        'c' => 'Out',
                    ])
                    ->hiddenLabel()
                    ->inline()
                    ->default('c')
                    ->live(),
                Forms\Components\Radio::make('currency')
                    ->options(UserCash::CURRENCIES)
                    ->default('SAR')
                    ->hiddenLabel()
                    ->inline()
                    ->required()
                    ->live()
                    ->afterStateUpdated(function ($state, $set) {
                        $set('exchange_rate', 1 / Currency::getExchangeRate($state));
                    }),
                Forms\Components\TextInput::make('amount')
                    ->numeric()
                    ->prefix(fn ($get) => $get('currency'))
                    ->required(),
                Forms\Components\TextInput::make('exchange_rate')
                    ->numeric()
                    ->default(1)
                    ->required()
                    ->live()
                    ->helperText(fn ($state, $get) => $state > 0 && $state < 1 ? 'SAR 1 = ' . $get('currency') . ' ' . round(1 / $state, 2) : null)
                    ->visible(fn ($get) => $get('currency') != 'SAR'),
                Forms\Components\FileUpload::make('attachment')
                    ->label('Bukti Transaksi')
                    ->required(fn ($get) => $get('type') == 'c')
                    ->validationMessages(['required' => 'Pengeluaran wajib menyertakan bukti transaksi'])
                    ->imageResizeTargetWidth('720')
                    ->imageResizeTargetHeight('720')
                    ->imageResizeMode('contain')
                    ->disk('s3')
                    ->directory('finance/attachments')
                    ->visibility('public'),
            ]);

    }

    public function table(Table $table): Table
    {
        return $table
            ->modelLabel('expense')
            ->modifyQueryUsing(fn ($query) => $query->with(['user', 'category']))
            ->columns([
                Tables\Columns\TextColumn::make('user.name'),
                Tables\Columns\TextColumn::make('cashed_at')
                    ->label('Date')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('details')
                    ->wrap()
                    ->description(fn ($record) => $record->category?->name ?? null)
                    ->searchable(),
                Tables\Columns\TextColumn::make('debit')
                    ->getStateUsing(fn ($record) => $record->type == 'd'
                    ? $record->amount_c
                    : null)
                    ->description(function ($record) {
                        return $record->type == 'd' && $record->currency != 'SAR'
                            ? money($record->amount, $record->currency)
                            : null;
                    })
                    ->currencyRight(),
                Tables\Columns\TextColumn::make('credit')
                    ->getStateUsing(fn ($record) => $record->type == 'c'
                    ? $record->amount_c
                    : null)
                    ->description(function ($record) {
                        return $record->type == 'c' && $record->currency != 'SAR'
                            ? money($record->amount, $record->currency)
                            : null;
                    })
                    ->currencyRight(),
                Tables\Columns\IconColumn::make('attachment')
                    ->attachment(),
            ])
            ->filters([
                Tables\Filters\Filter::make('category')
                    ->form([
                        SelectTree::make('id')
                            ->label('Category')
                            ->relationship('category', 'name', 'parent_id')
                            ->searchable(),
                    ])
                    ->query(function (Builder $query, array $data) {
                        return $query->when($data['id'], function ($query, $category_id) {
                            return $query->where('category_id', $category_id);
                        });
                    })
                    ->indicateUsing(function (array $data): ?string {
                        if (! $data['id']) {
                            return null;
                        }

                        return __('Category') . ': ' . CashCategory::find($data['id'])?->name;
                    }),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->label('Add'),
            ])
            ->actions([
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\EditAction::make()
                        ->mutateRecordDataUsing(function ($data) {
                            if (CashCategory::find($data['category_id'])?->group == ExpenseGroup::VendorPayment) {
                                $data['related_type'] = 'bill';
                            }

                            return $data;
                        })
                        ->visible(fn ($record) => ! $record->is_fixed),
                    Tables\Actions\DeleteAction::make()
                        ->visible(fn ($record) => ! $record->is_fixed),
                ]),
            ]);
    }
}
