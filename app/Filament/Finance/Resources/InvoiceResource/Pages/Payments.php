<?php

namespace App\Filament\Finance\Resources\InvoiceResource\Pages;

use App\Actions\Invoice\BulkGeneratePaymentPDF;
use App\Enums\Finance\AccountCategory;
use App\Enums\InvoiceStatus;
use App\Enums\PaymentMethod;
use App\Filament\Finance\Resources\InvoiceResource;
use App\Models\Currency;
use App\Models\Finance\CashAccount;
use App\Models\Finance\CreditNote;
use App\Models\Finance\InvoicePayment;
use Awcodes\Shout\Components\Shout;
use Carbon\Carbon;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Pages\ManageRelatedRecords;
use Filament\Tables;
use Filament\Tables\Table;

class Payments extends ManageRelatedRecords
{
    protected static string $resource = InvoiceResource::class;

    protected static string $relationship = 'payments';

    protected static ?string $navigationIcon = 'heroicon-o-banknotes';

    protected static ?string $navigationLabel = 'Payments';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Shout::make('deposit_balance_notice')
                    ->type('info')
                    ->content(fn ($get) => 'Deposit Balance: ' . money($get('deposit_balance'), 'SAR', true))
                    ->columnSpanFull()
                    ->visible(fn ($get, $context) => $context === 'create' && ($get('deposit_balance') > 0 || $get('payment_method') === 'deposit')),
                Forms\Components\Hidden::make('deposit_balance')
                    ->default(0),
                Forms\Components\ToggleButtons::make('payment_method')
                    ->grouped()
                    ->options(PaymentMethod::class)
                    ->default(PaymentMethod::Cash->value)
                    ->live()
                    ->afterStateUpdated(function ($state, $set) {
                        if ($state === 'deposit') {
                            $set('cash_account_id', CashAccount::getIdForCode(config('finance.coa.customer_deposit')));
                        } elseif ($state === 'credit_note') {
                            $set('cash_account_id', CashAccount::getIdForCode(config('finance.coa.credit_note')));
                        } else {
                            $set('cash_account_id', null);
                        }
                    }),
                Forms\Components\Select::make('cash_account_id')
                    ->label('Account')
                    ->options(function ($get) {
                        return CashAccount::query()
                            ->when(
                                $get('payment_method') === 'deposit',
                                fn ($query) => $query->where('code', config('finance.coa.customer_deposit')),
                                fn ($query) => $query->whereIn('category', [AccountCategory::CashBank, AccountCategory::AccountsPayable])
                            )
                            ->orderBy('code')
                            ->get()
                            ->mapWithKeys(function ($account) {
                                return [$account->id => $account->fullname];
                            });
                    })
                    ->required()
                    ->searchable()
                    ->disabled(fn ($get) => in_array($get('payment_method'), ['deposit', 'credit_note']))
                    ->dehydrated(),
                Forms\Components\Select::make('credit_note_id')
                    ->label('Credit note')
                    ->options(fn () => CreditNote::query()
                        ->with('invoice')
                        ->where('customer_id', $this->getOwnerRecord()->customer_id)
                        ->whereNull('used_for_invoice_id')
                        ->orderBy('invoice_date')
                        ->get()
                        ->mapWithKeys(function ($creditNote) {
                            return [$creditNote->id => $creditNote->invoice->invoice_number . ' (' . money($creditNote->amount, $creditNote->currency_code, true) . ')'];
                        })
                    )
                    ->required()
                    ->searchable()
                    ->live()
                    ->afterStateUpdated(function ($state, $set) {
                        $creditNote = CreditNote::find($state);
                        if ($creditNote) {
                            $set('currency_code', $creditNote->currency_code);
                            $set('amount', $creditNote->amount);
                            $set('exchange_rate', $creditNote->exchange_rate);
                        }
                    })
                    ->visible(fn ($get) => $get('payment_method') === 'credit_note'),
                Forms\Components\DateTimePicker::make('paid_at')
                    ->label('Date')
                    ->default(now())
                    ->maxDate(today()->addDay())
                    ->required(),
                Forms\Components\TextInput::make('description')
                    ->autocomplete('off')
                    ->maxLength(255),

                Forms\Components\ToggleButtons::make('currency_code')
                    ->label('Currency')
                    ->default('SAR')
                    ->options(Currency::getOptions())
                    ->inline()
                    ->live()
                    ->afterStateUpdated(function ($state, $set) {
                        $set('exchange_rate', 1 / Currency::getExchangeRate($state));
                    })
                    ->disabled(fn ($get) => $get('payment_method') === 'credit_note')
                    ->dehydrated(),

                Forms\Components\TextInput::make('amount')
                    ->numeric()
                    ->required()
                    ->prefix(fn ($get) => $get('currency_code') ?? 'SAR')
                    ->disabled(fn ($get) => $get('payment_method') === 'credit_note')
                    ->dehydrated(),

                Forms\Components\TextInput::make('exchange_rate')
                    ->required()
                    ->numeric()
                    ->live()
                    ->helperText(fn ($state, $get) => $state > 0 && $state < 1 ? 'SAR 1 = ' . $get('currency_code') . ' ' . round(1 / $state, 2) : null)
                    ->disabled(fn ($get) => $get('currency_code') === 'SAR')
                    ->dehydrated(),

                Forms\Components\FileUpload::make('attachment')
                    ->imageResizeTargetWidth('720')
                    ->imageResizeTargetHeight('720')
                    ->imageResizeMode('contain')
                    ->disk('s3')
                    ->directory('invoice/payments')
                    ->visibility('public'),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->modelLabel('payment')
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label('No.'),
                Tables\Columns\TextColumn::make('paid_at')
                    ->label('Date')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('payment_method')
                    ->label('Via')
                    ->badge(),
                Tables\Columns\TextColumn::make('cash_account.fullname')
                    ->label('Account'),
                Tables\Columns\TextColumn::make('description')
                    ->wrap(),
                Tables\Columns\IconColumn::make('attachment')
                    ->attachment(),
                Tables\Columns\TextColumn::make('amount')
                    ->currencyAuto(),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->mountUsing(function ($form) {
                        $customer = $this->getOwnerRecord()->customer;
                        $depositBalance = $customer->deposit_balance;
                        $accountId = CashAccount::query()
                            ->where('code', $depositBalance > 0 ? config('finance.coa.customer_deposit') : config('finance.coa.main_cash'))
                            ->value('id');
                        $form->fill([
                            'deposit_balance' => $depositBalance,
                            'payment_method' => $depositBalance > 0 ? 'deposit' : 'cash',
                            'paid_at' => Carbon::now(),
                            'cash_account_id' => $accountId,
                            'currency_code' => $this->getOwnerRecord()->currency_code,
                            'exchange_rate' => 1 / Currency::getExchangeRate($this->getOwnerRecord()->currency_code),
                        ]);
                    })
                    ->mutateFormDataUsing(function ($data) {
                        unset($data['deposit_balance']);

                        return $data;
                    })
                    ->visible(fn ($livewire) => $livewire->getOwnerRecord()->status !== InvoiceStatus::Cancelled),
            ])
            ->actions([
                InvoicePayment::getReceiptTableAction(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkAction::make('download')
                    ->icon('heroicon-o-arrow-down-tray')
                    ->color('gray')
                    ->action(function ($records) {
                        return response()->download(BulkGeneratePaymentPDF::run($records));
                    })
                    ->deselectRecordsAfterCompletion(),
            ])
            ->defaultSort('paid_at', 'desc');
    }
}
