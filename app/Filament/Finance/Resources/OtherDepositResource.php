<?php

namespace App\Filament\Finance\Resources;

use App\Enums\CashTransactionType;
use App\Filament\Actions\ActivityLogTimelineTableAction;
use App\Filament\Finance\Resources\OtherDepositResource\Pages;
use App\Models\Currency;
use App\Models\Finance\CashAccount;
use App\Models\Finance\CashTransaction;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Enums\FiltersLayout;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Malzariey\FilamentDaterangepickerFilter\Filters\DateRangeFilter;

class OtherDepositResource extends Resource
{
    protected static ?string $model = CashTransaction::class;

    protected static ?string $modelLabel = 'other deposit';

    protected static ?string $navigationGroup = 'Cash & Bank';

    protected static ?int $navigationSort = 10;

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->where('type', CashTransactionType::In);
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Tabs::make()
                    ->tabs([
                        Forms\Components\Tabs\Tab::make('Details')
                            ->columns()
                            ->schema([
                                Forms\Components\Hidden::make('type')
                                    ->default('in'),
                                Forms\Components\DatePicker::make('transaction_date')
                                    ->default(now())
                                    ->required(),
                                Forms\Components\TextInput::make('details'),
                                Forms\Components\Select::make('debit_account_id')
                                    ->label('Cash/Bank Account')
                                    ->options(fn () => CashAccount::query()
                                        ->isCashOrBank()
                                        ->orderBy('code')
                                        ->get()
                                        ->mapWithKeys(function ($account) {
                                            return [$account->id => $account->fullname];
                                        })
                                    )
                                    ->required()
                                    ->searchable(),
                                Forms\Components\Select::make('credit_account_id')
                                    ->label('Income Account')
                                    ->options(fn () => CashAccount::query()
                                        ->isNotCashOrBank()
                                        ->orderBy('code')
                                        ->get()
                                        ->mapWithKeys(function ($account) {
                                            return [$account->id => $account->fullname];
                                        })
                                    )
                                    ->required()
                                    ->searchable(),
                                Forms\Components\ToggleButtons::make('currency_code')
                                    ->label('Currency')
                                    ->options(Currency::getOptions())
                                    ->default('SAR')
                                    ->grouped()
                                    ->required()
                                    ->live()
                                    ->afterStateUpdated(function ($state, $set) {
                                        $set('exchange_rate', 1 / Currency::getExchangeRate($state));
                                    }),
                                Forms\Components\TextInput::make('amount')
                                    ->numeric()
                                    ->prefix(fn ($get) => $get('currency_code'))
                                    ->required(),
                                Forms\Components\TextInput::make('exchange_rate')
                                    ->numeric()
                                    ->default(1)
                                    ->required()
                                    ->live()
                                    ->helperText(fn ($state, $get) => $state > 0 && $state < 1 ? 'SAR 1 = ' . $get('currency_code') . ' ' . round(1 / $state, 2) : null)
                                    ->visible(fn ($get) => $get('currency_code') != 'SAR'),
                            ]),
                        Forms\Components\Tabs\Tab::make('Attachments')
                            ->schema([
                                Forms\Components\FileUpload::make('attachments')
                                    ->hiddenLabel()
                                    ->multiple()
                                    ->panelLayout('grid')
                                    ->imageResizeTargetWidth('720')
                                    ->imageResizeTargetHeight('720')
                                    ->imageResizeMode('contain')
                                    ->disk('s3')
                                    ->directory('finance/attachments')
                                    ->visibility('public'),
                            ]),
                    ]),
            ])
            ->columns(1);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->modifyQueryUsing(fn ($query) => $query->with(['debit_account', 'credit_account']))
            ->columns([
                Tables\Columns\TextColumn::make('transaction_date')
                    ->label('Date')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('debit_account.name')
                    ->label('Cash/Bank'),
                Tables\Columns\TextColumn::make('credit_account.name')
                    ->label('Income'),
                Tables\Columns\TextColumn::make('details')
                    ->wrap()
                    ->searchable(),
                Tables\Columns\TextColumn::make('amount')
                    ->currencyAuto(),
                Tables\Columns\IconColumn::make('attachment')
                    ->attachments(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('account_id')
                    ->label('Cash/Bank')
                    ->native(false)
                    ->options(fn () => CashAccount::query()
                        ->isCashOrBank()
                        ->orderBy('code')
                        ->get()
                        ->mapWithKeys(function ($account) {
                            return [$account->id => $account->fullname];
                        })
                    )
                    ->modifyQueryUsing(fn ($query, $data) => $query
                        ->when($data['value'] ?? null, fn ($query, $value) => $query
                            ->where('debit_account_id', $value))),
                DateRangeFilter::make('transaction_date')
                    ->label('Date range')
                    ->withIndicator()
                    ->indicateUsing(function ($data, $filter) {
                        $datesString = data_get($data, 'transaction_date');

                        if (empty($datesString)) {
                            return null;
                        }

                        return "Date range: {$datesString}";
                    })
                    ->columnSpan(2),
            ])
            ->filtersLayout(FiltersLayout::AboveContent)
            ->actions([
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\EditAction::make(),
                    Tables\Actions\DeleteAction::make(),
                    Tables\Actions\ActionGroup::make([
                        ActivityLogTimelineTableAction::make('history'),
                    ])
                        ->dropdown(false),
                ]),
            ])
            ->defaultSort('transaction_date', 'desc');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageOtherDeposits::route('/'),
        ];
    }
}
