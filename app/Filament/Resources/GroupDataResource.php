<?php

namespace App\Filament\Resources;

use App\Filament\Resources\GroupDataResource\Pages;
use App\Models\Group;
use App\Models\GroupData;
use App\Models\User;
use Awcodes\TableRepeater\Components\TableRepeater;
use Awcodes\TableRepeater\Header;
use Carbon\Carbon;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class GroupDataResource extends Resource
{
    protected static ?string $model = GroupData::class;

    protected static ?string $navigationGroup = 'Groups';

    protected static ?int $navigationSort = -1;

    protected static ?string $modelLabel = 'group data';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('group_id')
                    ->label('Group')
                    ->options(fn (?GroupData $record) => Group::query()
                        ->currentPeriod()
                        ->confirmedOrDraft()
                        ->where(function ($query) use ($record) {
                            $query->whereDoesntHave('group_data')
                                ->orWhere('id', $record?->group_id ?? 0);
                        })
                        ->get()
                        ->mapWithKeys(function (Group $group) {
                            return [$group->id => $group->full_name];
                        }))
                    ->required()
                    ->searchable()
                    ->columnSpan(2),
                Forms\Components\FileUpload::make('visa')
                    ->label('Visa File')
                    ->previewable(false)
                    ->hidden(fn ($record) => $record ? ! $record->visa : true),
                Forms\Components\FileUpload::make('ticket')
                    ->label('Ticket File')
                    ->previewable(false)
                    ->hidden(fn ($record) => $record ? ! $record->ticket : true),
                Forms\Components\FileUpload::make('roomlist')
                    ->label('Roomlist File')
                    ->previewable(false)
                    ->hidden(fn ($record) => $record ? ! $record->roomlist : true),
                TableRepeater::make('files')
                    ->label('Files')
                    ->headers([
                        Header::make('Name'),
                        Header::make('File'),
                    ])
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->datalist(GroupData::FILES)
                            ->required(),
                        Forms\Components\FileUpload::make('file')
                            ->previewable(false),
                    ])
                    ->defaultItems(0)
                    ->columnSpan('full')
                    ->addActionLabel('Add File'),
            ])
            ->columns(2);
    }

    public static function getEloquentQuery(): Builder
    {
        /** @var User */
        $user = auth()->user();

        return GroupData::query()
            ->with(['group', 'muassasah', 'customer'])
            ->whereHas('group', function ($query) use ($user) {
                $query
                    ->currentPeriod()
                    ->when($user->hasExactRoles('Airport Handler'), function ($query) use ($user) {
                        $query
                            ->whereHas('flights', function ($query) use ($user) {
                                $userVendorIds = $user->vendors()->pluck('id');
                                $query->whereIn('handler_id', $userVendorIds);
                            });
                    });
            })
            ->join('groups', 'groups.id', '=', 'group_data.group_id')
            ->select(['group_data.*', 'groups.arrival_date AS arrival_date']);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('group')
                    ->label('Group')
                    ->url(fn (GroupData $record) => GroupResource::getUrl('view', ['record' => $record->group]))
                    ->getStateUsing(fn ($record) => $record->customer->name)
                    ->description(fn ($record) => $record->group->name),
                Tables\Columns\TextColumn::make('muassasah.company_name')
                    ->label('Muassasah'),
                Tables\Columns\TextColumn::make('arrival_date')
                    ->label('Arrival Date')
                    ->date()
                    ->sortable(),
                Tables\Columns\ViewColumn::make('files')
                    ->label('Files')
                    ->disabledClick()
                    ->view('filament.tables.columns.group-data-files'),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('customer')
                    ->relationship('customer', 'name')
                    ->searchable()
                    ->preload(),
                Tables\Filters\SelectFilter::make('services')
                    ->options(Group::GROUP_SERVICES)
                    ->multiple()
                    ->query(function ($query, $data) {
                        if (filled($data['values'])) {
                            $query->whereHas('group', fn ($query) => $query->where(function ($query) use ($data) {
                                collect($data['values'])
                                    ->each(function ($value) use ($query) {
                                        $query
                                            ->where('services', 'like', '%' . $value . '%');
                                    });
                            }));
                        }

                        return $query;
                    }),
                Tables\Filters\SelectFilter::make('service')
                    ->options(array_merge(['full' => 'Full LA'], Group::GROUP_SERVICES))
                    ->query(function ($query, $data) {
                        if (filled($data['value'])) {
                            if ($data['value'] == 'full') {
                                return $query
                                    ->whereHas(
                                        'group',
                                        fn ($query) => $query
                                            ->where('services', 'like', '%handling%')
                                            ->where('services', 'like', '%hotel%')
                                            ->where('services', 'like', '%visa%')
                                    );
                            }

                            return $query
                                ->whereHas(
                                    'group',
                                    fn ($query) => $query
                                        ->where('services', 'like', '%' . $data['value'] . '%')
                                );
                        }

                        return $query;
                    }),
                Tables\Filters\SelectFilter::make('muassasah')
                    ->label('Muassasah')
                    ->relationship('muassasah', 'company_name')
                    ->searchable(),
                Tables\Filters\Filter::make('arrival_date')
                    ->form([
                        Forms\Components\DatePicker::make('from')
                            ->label('Arrival from'),
                        Forms\Components\DatePicker::make('until')
                            ->label('Arrival until'),
                    ])
                    ->query(function (Builder $query, array $data) {
                        return $query
                            ->when(
                                $data['from'],
                                function (Builder $query, $date): Builder {
                                    return $query->whereHas(
                                        'group',
                                        fn ($query) => $query->whereDate('arrival_date', '>=', $date)
                                    );
                                }
                            )
                            ->when(
                                $data['until'],
                                function (Builder $query, $date): Builder {
                                    return $query->whereHas(
                                        'group',
                                        fn ($query) => $query->whereDate('arrival_date', '<=', $date)
                                    );
                                }
                            );
                    })
                    ->indicateUsing(function (array $data): array {
                        $indicators = [];

                        if ($data['from'] ?? null) {
                            $indicators['from'] = 'Arrival from ' . Carbon::parse($data['from'])->format('d-M-y');
                        }

                        if ($data['until'] ?? null) {
                            $indicators['until'] = 'Arrival until ' . Carbon::parse($data['until'])->format('d-M-y');
                        }

                        return $indicators;
                    }),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                // Tables\Actions\DeleteBulkAction::make(),
            ])
            ->defaultSort('arrival_date', 'desc');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageGroupData::route('/'),
        ];
    }
}
