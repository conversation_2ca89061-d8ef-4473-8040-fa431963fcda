<?php

namespace App\Models;

use App\Models\Concerns\HasJournalEntry;
use App\Models\Contracts\JournalTransaction;
use App\Models\Finance\CashAccount;
use App\Models\Finance\UserCash;
use App\Models\Traits\DefaultLogOptions;
use App\Models\Traits\WithUserstamps;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Spatie\Activitylog\Traits\LogsActivity;

class BillPayment extends Model implements JournalTransaction
{
    use DefaultLogOptions;
    use HasFactory;
    use HasJournalEntry;
    use LogsActivity;
    use WithUserstamps;

    protected $fillable = [
        'bill_id',

        'user_id',
        'cash_account_id',

        'user_cash_id',

        'paid_at',
        'amount',
        'currency_code',
        'exchange_rate',

        'description',
        'attachment',

        'created_by_id',
        'updated_by_id',
    ];

    protected $casts = [
        'paid_at' => 'datetime',
        'amount' => 'float',
        'exchange_rate' => 'float',
    ];

    public function getTransactionType(): string
    {
        return 'Bill Payment';
    }

    public function getTransactionNumber(): string
    {
        return '#' . str_pad($this->id, 6, '0', STR_PAD_LEFT);
    }

    public function getTransactionDetailAttribute(): ?string
    {
        return $this->description;
    }

    public function amountConverted(): Attribute
    {
        return Attribute::get(function () {
            if ($this->currency_code == $this->bill->currency_code) {
                return $this->amount;
            }

            $baseCurrency = config('finance.base_currency');

            return $this->currency_code == $baseCurrency
                ? $this->amount / $this->bill->exchange_rate
                : $this->amount * $this->exchange_rate / $this->bill->exchange_rate;
        });
    }

    protected static function boot()
    {
        static::creating(function (self $model) {
            $model->user_id ??= auth('web')->id();
            $model->exchange_rate ??= 1;
        });
        static::saving(function (self $model) {
            $model->cash_account_id ??= CashAccount::query()
                ->where('code', config('finance.coa.main_cash'))
                ->value('id');
        });
        static::saved(function (self $model) {
            $model->bill?->save();
            $model->syncJournalEntry();
        });
        static::deleted(fn ($model) => $model->bill?->save());

        parent::boot();
    }

    public function bill(): BelongsTo
    {
        return $this->belongsTo(Bill::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function cash_account(): BelongsTo
    {
        return $this->belongsTo(CashAccount::class);
    }

    public function user_cash(): BelongsTo
    {
        return $this->belongsTo(UserCash::class);
    }

    public function syncJournalEntry(): void
    {
        /** @var JournalEntry $entry */
        $entry = $this->journal_entry()->updateOrCreate([], [
            'entry_date' => $this->paid_at,
            'details' => 'Bill Payment ' . ($this->bill?->bill_number ?? '') . " (#{$this->id})",
        ]);
        $entry->items()->updateOrCreate([
            'type' => 'd',
        ], [
            'account_id' => CashAccount::query()
                ->where('code', config('finance.coa.payable')) // utang usaha
                ->value('id'),
            'amount' => $this->amount * $this->exchange_rate,
        ]);
        // TODO: move this to settings
        $advanceCashId = CashAccount::query()
            ->where('code', config('finance.coa.advance_cash'))
            ->value('id');
        $entry->items()->updateOrCreate([
            'type' => 'c',
        ], [
            'account_id' => $this->cash_account_id ?? CashAccount::query()
                ->where('code', config('finance.coa.main_cash')) // kas besar
                ->value('id'),
            'amount' => $this->amount * $this->exchange_rate,
            'owner_type' => $this->cash_account_id == $advanceCashId ? 'user' : null,
            'owner_id' => $this->cash_account_id == $advanceCashId ? $this->user_id : null,
        ]);
    }

    public function delete()
    {
        $this->journal_entry?->delete();

        return parent::delete();
    }
}
