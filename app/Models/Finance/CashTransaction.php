<?php

namespace App\Models\Finance;

use App\Enums\CashTransactionType;
use App\Models\Concerns\HasJournalEntry;
use App\Models\Contracts\JournalTransaction;
use App\Models\Traits\DefaultLogOptions;
use App\Models\Traits\WithUserstamps;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Spatie\Activitylog\Traits\LogsActivity;

class CashTransaction extends Model implements JournalTransaction
{
    use DefaultLogOptions;
    use HasFactory;
    use HasJournalEntry;
    use LogsActivity;
    use WithUserstamps;

    protected $fillable = [
        'transaction_date',

        'type',

        'amount',
        'currency_code',
        'exchange_rate',
        'details',
        'attachments',

        'debit_account_id',
        'credit_account_id',

        'created_by_id',
        'updated_by_id',
    ];

    public function getTransactionType(): string
    {
        return match ($this->type) {
            CashTransactionType::Out => 'Other Payment',
            CashTransactionType::In => 'Other Deposit',
            CashTransactionType::Transfer => 'Cash Transfer'
        };
    }

    public function getTransactionNumber(): string
    {
        return '#' . str_pad($this->id, 6, '0', STR_PAD_LEFT);
    }

    public function getTransactionDetailAttribute(): ?string
    {
        return $this->details;
    }

    protected function casts()
    {
        return [
            'transaction_date' => 'datetime',
            'type' => CashTransactionType::class,
            'amount' => 'float',
            'amount_c' => 'float',
            'exchange_rate' => 'float',
            'attachments' => 'array',
        ];
    }

    protected static function booted()
    {
        static::creating(function (self $model) {
            $model->exchange_rate ??= 1;
        });
        static::saved(function (self $model) {
            $model->syncJournalEntry();
        });
    }

    public function debit_account(): BelongsTo
    {
        return $this->belongsTo(CashAccount::class);
    }

    public function credit_account(): BelongsTo
    {
        return $this->belongsTo(CashAccount::class);
    }

    public function syncJournalEntry(): void
    {
        /** @var JournalEntry $entry */
        $entry = $this->journal_entry()->updateOrCreate([], [
            'entry_date' => $this->transaction_date,
            'details' => $this->details,
        ]);
        $entry->items()->updateOrCreate([
            'type' => 'd',
        ], [
            'account_id' => $this->debit_account_id,
            'amount' => $this->amount * $this->exchange_rate,
        ]);
        $entry->items()->updateOrCreate([
            'type' => 'c',
        ], [
            'account_id' => $this->credit_account_id,
            'amount' => $this->amount * $this->exchange_rate,
        ]);
    }

    public function delete()
    {
        $this->journal_entry?->delete();

        return parent::delete();
    }
}
