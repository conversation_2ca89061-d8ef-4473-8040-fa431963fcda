<?php

namespace App\Enums;

use Filament\Support\Contracts\HasLabel;

enum PaymentMethod: string implements HasLabel
{
    case Cash = 'cash';
    case BankTransfer = 'bank';
    case Deposit = 'deposit';
    case CreditNote = 'credit_note';
    case DebitNote = 'debit_note';

    public function getLabel(): ?string
    {
        return match ($this) {
            self::Cash => 'Cash',
            self::BankTransfer => 'Bank Transfer',
            self::Deposit => 'Deposit',
            self::CreditNote => 'Credit Note',
            self::DebitNote => 'Debit Note'
        };
    }
}
