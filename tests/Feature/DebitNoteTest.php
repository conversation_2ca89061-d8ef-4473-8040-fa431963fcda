<?php

namespace Tests\Feature;

use App\Models\Bill;
use App\Models\BillPayment;
use App\Models\Finance\CashAccount;
use App\Models\Finance\DebitNote;
use App\Models\Vendor;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class DebitNoteTest extends TestCase
{
    use RefreshDatabase;

    public function test_debit_note_is_created_when_bill_is_overpaid()
    {
        // Create necessary accounts
        CashAccount::factory()->create([
            'code' => config('finance.coa.payable'),
            'name' => 'Accounts Payable',
        ]);
        CashAccount::factory()->create([
            'code' => config('finance.coa.debit_note'),
            'name' => 'Debit Note',
        ]);
        CashAccount::factory()->create([
            'code' => config('finance.coa.main_cash'),
            'name' => 'Main Cash',
        ]);

        // Create a vendor and bill
        $vendor = Vendor::factory()->create();
        $bill = Bill::factory()->create([
            'vendor_id' => $vendor->id,
            'total' => 1000,
            'currency_code' => 'SAR',
            'exchange_rate' => 1,
        ]);

        // Create an overpayment
        BillPayment::factory()->create([
            'bill_id' => $bill->id,
            'amount' => 1200, // Overpaid by 200
            'currency_code' => 'SAR',
            'exchange_rate' => 1,
        ]);

        // Refresh the bill to trigger the saved event
        $bill->refresh();

        // Assert that a debit note was created
        $this->assertDatabaseHas('debit_notes', [
            'vendor_id' => $vendor->id,
            'bill_id' => $bill->id,
            'amount' => 200,
            'currency_code' => 'SAR',
            'exchange_rate' => 1,
        ]);

        // Assert that the bill's paid amount was adjusted
        $this->assertEquals(1000, $bill->paid);
    }

    public function test_debit_note_is_deleted_when_overpayment_is_corrected()
    {
        // Create necessary accounts
        CashAccount::factory()->create([
            'code' => config('finance.coa.payable'),
            'name' => 'Accounts Payable',
        ]);
        CashAccount::factory()->create([
            'code' => config('finance.coa.debit_note'),
            'name' => 'Debit Note',
        ]);
        CashAccount::factory()->create([
            'code' => config('finance.coa.main_cash'),
            'name' => 'Main Cash',
        ]);

        // Create a vendor and bill
        $vendor = Vendor::factory()->create();
        $bill = Bill::factory()->create([
            'vendor_id' => $vendor->id,
            'total' => 1000,
            'currency_code' => 'SAR',
            'exchange_rate' => 1,
        ]);

        // Create an overpayment
        $payment = BillPayment::factory()->create([
            'bill_id' => $bill->id,
            'amount' => 1200, // Overpaid by 200
            'currency_code' => 'SAR',
            'exchange_rate' => 1,
        ]);

        // Refresh the bill to trigger the saved event
        $bill->refresh();

        // Assert that a debit note was created
        $this->assertDatabaseHas('debit_notes', [
            'vendor_id' => $vendor->id,
            'bill_id' => $bill->id,
            'amount' => 200,
        ]);

        // Correct the payment amount
        $payment->update(['amount' => 1000]);
        $bill->refresh();

        // Assert that the debit note was deleted
        $this->assertDatabaseMissing('debit_notes', [
            'vendor_id' => $vendor->id,
            'bill_id' => $bill->id,
        ]);
    }

    public function test_debit_note_relationships()
    {
        // Create necessary accounts
        CashAccount::factory()->create([
            'code' => config('finance.coa.payable'),
            'name' => 'Accounts Payable',
        ]);
        CashAccount::factory()->create([
            'code' => config('finance.coa.debit_note'),
            'name' => 'Debit Note',
        ]);

        // Create a vendor and bill
        $vendor = Vendor::factory()->create();
        $bill = Bill::factory()->create([
            'vendor_id' => $vendor->id,
        ]);

        // Create a debit note
        $debitNote = DebitNote::factory()->create([
            'vendor_id' => $vendor->id,
            'bill_id' => $bill->id,
            'amount' => 100,
        ]);

        // Test relationships
        $this->assertEquals($vendor->id, $debitNote->vendor->id);
        $this->assertEquals($bill->id, $debitNote->bill->id);
        $this->assertTrue($vendor->debitNotes->contains($debitNote));
        $this->assertEquals($debitNote->id, $bill->debitNote->id);
    }
}
