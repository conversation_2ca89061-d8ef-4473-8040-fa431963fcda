<?php

use App\Models\Bill;
use App\Models\BillPayment;
use App\Models\Finance\CashAccount;
use App\Models\Finance\DebitNote;
use App\Models\Vendor;

uses(Illuminate\Foundation\Testing\RefreshDatabase::class);

beforeEach(function () {
    // Create necessary accounts
    CashAccount::factory()->create([
        'code' => config('finance.coa.payable'),
        'name' => 'Accounts Payable',
    ]);
    CashAccount::factory()->create([
        'code' => config('finance.coa.debit_note'),
        'name' => 'Debit Note',
    ]);
    CashAccount::factory()->create([
        'code' => config('finance.coa.main_cash'),
        'name' => 'Main Cash',
    ]);
});

it('creates debit note when bill is overpaid', function () {
    // Create a vendor and bill
    $vendor = Vendor::factory()->create();
    $bill = Bill::factory()->create([
        'vendor_id' => $vendor->id,
        'currency_code' => 'SAR',
        'exchange_rate' => 1,
    ]);

    // Manually set the total using direct database update to avoid triggering events
    DB::table('bills')->where('id', $bill->id)->update(['total' => 1000]);
    $bill->refresh();

    // Debug: Check the bill's total and paid amounts before payment
    expect($bill->total)->toBe(1000.0);
    expect($bill->paid)->toBe(0.0);

    // Create an overpayment
    BillPayment::factory()->create([
        'bill_id' => $bill->id,
        'amount' => 1200, // Overpaid by 200
        'currency_code' => 'SAR',
        'exchange_rate' => 1,
    ]);

    // Refresh the bill to trigger the saved event
    $bill->refresh();

    // Debug: Check the bill's total and paid amounts after payment
    dump("Bill total: {$bill->total}, Bill paid: {$bill->paid}");

    // Assert that a debit note was created
    $this->assertDatabaseHas('debit_notes', [
        'vendor_id' => $vendor->id,
        'bill_id' => $bill->id,
        'amount' => 200,
        'currency_code' => 'SAR',
        'exchange_rate' => 1,
    ]);

    // Assert that the bill's paid amount was adjusted
    expect($bill->paid)->toBe(1000.0);
});

it('deletes debit note when overpayment is corrected', function () {
    // Create a vendor and bill
    $vendor = Vendor::factory()->create();
    $bill = Bill::factory()->create([
        'vendor_id' => $vendor->id,
        'currency_code' => 'SAR',
        'exchange_rate' => 1,
    ]);

    // Manually set the total using direct database update to avoid triggering events
    DB::table('bills')->where('id', $bill->id)->update(['total' => 1000]);
    $bill->refresh();

    // Create an overpayment
    $payment = BillPayment::factory()->create([
        'bill_id' => $bill->id,
        'amount' => 1200, // Overpaid by 200
        'currency_code' => 'SAR',
        'exchange_rate' => 1,
    ]);

    // Refresh the bill to trigger the saved event
    $bill->refresh();

    // Assert that a debit note was created
    $this->assertDatabaseHas('debit_notes', [
        'vendor_id' => $vendor->id,
        'bill_id' => $bill->id,
        'amount' => 200,
    ]);

    // Correct the payment amount
    $payment->update(['amount' => 1000]);
    $bill->refresh();

    // Assert that the debit note was deleted
    $this->assertDatabaseMissing('debit_notes', [
        'vendor_id' => $vendor->id,
        'bill_id' => $bill->id,
    ]);
});

it('has correct relationships', function () {
    // Create a vendor and bill
    $vendor = Vendor::factory()->create();
    $bill = Bill::factory()->create([
        'vendor_id' => $vendor->id,
    ]);

    // Create a debit note
    $debitNote = DebitNote::factory()->create([
        'vendor_id' => $vendor->id,
        'bill_id' => $bill->id,
        'amount' => 100,
    ]);

    // Test relationships
    expect($debitNote->vendor->id)->toBe($vendor->id);
    expect($debitNote->bill->id)->toBe($bill->id);
    expect($vendor->debitNotes->contains($debitNote))->toBeTrue();
    expect($bill->debitNote->id)->toBe($debitNote->id);
});
