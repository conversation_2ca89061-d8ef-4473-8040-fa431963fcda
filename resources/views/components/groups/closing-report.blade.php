@props([
    'group' => null,
    'data' => [],
])

@php
    extract($data);
    $currencyCode = $currency_code ?? 'SAR';
    $exchangeRate = $exchange_rate ?? 1;
@endphp

<style>
    .doc-report .title {
        font-size: 1.75rem;
        font-weight: bold;
        margin-bottom: 0.25rem;
        text-align: center;
    }
    .doc-report .subtitle {
        font-size: 1.125rem;
        font-weight: bold;
        margin-bottom: 1rem;
        text-align: center;
    }
    .doc-report table {
        width: 100%;
    }
    .doc-report table th {
        text-align: left;
    }
    .doc-report .border-bottom {
        border-bottom: 1px solid gray;
    }
    .doc-report .sep {
        width: 100%;
        height: 1rem;
    }
    .doc-report .text-sm {
        font-size: 10pt;
    }
    .doc-report .tabular-nums text-right {
        text-align: right;
    }
    .doc-report .is-profit {
        color: green;
    }
    .doc-report .is-loss {
        color: red;
    }
    .doc-report .nowrap {
        white-space: nowrap;
    }
</style>
<div class="doc-report">
    <h1 class="title">CLOSING REPORT</h1>
    <h3 class="subtitle">GROUP #{{ $group->id }} &ndash; {{ $group->customer->name }} ({{ $group->name }})</h3>

    <table style="margin: 0 1cm;">
        <tr>
            <th style="width: 6cm;">PAX</th>
            <td>: {{ $group->total_pax }} pax</td>
        </tr>
        @if ($group->arrival_date)
        <tr>
            <th>DATE</th>
            <td>: {{ $group->arrival_date->format('j F Y') }}
                @if ($group->departure_date)
                    &ndash; {{ $group->departure_date->format('j F Y') }}
                @endif
            </td>
        </tr>
        @endif
        <tr>
            <th>PACKAGE</th>
            <td>: {{ $group->getServices() }}</td>
        </tr>
    </table>
    <div class="sep"></div>
    <table class="trx">
        <tr>
            <td colspan="3"><strong>INVOICES</strong></td>
        </tr>
        @php $total_invoice = 0; @endphp
        @foreach ($group->invoices as $invoice)
        @php $total = $invoice->total * $invoice->exchange_rate; @endphp
            @foreach ($invoice->items as $item)
            <tr>
                <td></td>
                <td class="border-bottom">{{ $invoice->invoice_number }} - {{ $item->name }}</td>
                <td class="text-right tabular-nums border-bottom">{{ money(($item->unit_price * $item->quantity) * $invoice->exchange_rate * $exchangeRate, $currencyCode, true) }}</td>
            </tr>
            @endforeach
        @php $total_invoice += $total; @endphp
        @endforeach
        <tr>
            <td></td>
            <td><strong>Total Invoice</strong></td>
            <td class="text-right tabular-nums"><strong>{{ money($total_invoice * $exchangeRate, $currencyCode, true) }}</strong></td>
        </tr>
        <tr>
            <td colspan="3"><div class="sep"></div></td>
        </tr>
        <tr>
            <td colspan="3"><strong>REVENUE</strong></td>
        </tr>
        @foreach ($payments as $payment)
        <tr>
            <td style="width: 0.5cm;"></td>
            @if ($payment instanceof \App\Models\GroupCash)
            <td class="border-bottom">
                <p>Payment ({{ $payment->cashed_at->format('j F Y')}})</p>
                <p class="text-sm">{{ $payment->description }}</p>
            </td>
            <td class="text-right tabular-nums border-bottom">{{ money(($payment->cash_in_c ?? 0) * $exchangeRate, $currencyCode, true) }}</td>
            @else
            <td class="border-bottom">
                <p>Payment ({{ $payment->paid_at->format('j F Y')}})</p>
                <p class="text-sm">{{ $payment->description }}</p>
            </td>
            <td class="text-right tabular-nums border-bottom">{{ money($payment->amount * $payment->exchange_rate * $exchangeRate, $currencyCode, true) }}</td>
            @endif
        </tr>
        @endforeach
        @foreach ($creditNotes as $note)
        <tr>
            <td style="width: 0.5cm;"></td>
            <td class="border-bottom">
                <p>Credit Note for Invoice {{ $note->invoice->invoice_number }}</p>
            </td>
            <td class="text-right tabular-nums border-bottom">-{{ money($note->amount * $note->exchange_rate * $exchangeRate, $currencyCode, true) }}</td>
        </tr>
        @endforeach
        <tr>
            <td></td>
            <td><strong>Total Income</strong></td>
            <td class="text-right tabular-nums"><strong>{{ money(($total_income ?? 0) * $exchangeRate, $currencyCode, true) }}</strong></td>
        </tr>
        <tr>
            <td colspan="3"><div class="sep"></div></td>
        </tr>
        <tr>
            <td colspan="3"><strong>EXPENDITURE</strong></td>
        </tr>
        @foreach ($bill_items as $item)
        <tr>
            <td></td>
            <td class="border-bottom">
                {{ $item->bill->bill_number }} - {{ $item->name }}
            </td>
            <td class="text-right tabular-nums border-bottom">{{ money($item->unit_price * $item->quantity * (1 + $item->vat / 100) * $item->bill->exchange_rate * $exchangeRate, $currencyCode, true) }}</td>
        </tr>
        @endforeach
        @foreach ($expenses as $key => $value)
        <tr>
            <td></td>
            <td class="border-bottom">{{ $key }}</td>
            <td class="text-right tabular-nums border-bottom">{{ money(($value ?? 0) * $exchangeRate, $currencyCode, true) }}</td>
        </tr>
        @endforeach
        <tr>
            <td></td>
            <td><strong>Total Expense</strong></td>
            <td class="text-right tabular-nums"><strong>{{ money(($total_expense ?? 0) * $exchangeRate, $currencyCode, true) }}</strong></td>
        </tr>
        <tr>
            <td colspan="3"><div class="sep"></div></td>
        </tr>
        <tr>
            <td colspan="3"><strong>TOTAL</strong></td>
        </tr>
        <tr>
            <td></td>
            <td class="border-bottom">Income</td>
            <td class="text-right tabular-nums border-bottom">{{ money(($total_income ?? 0) * $exchangeRate, $currencyCode, true) }}</td>
        </tr>
        <tr>
            <td></td>
            <td class="border-bottom">Expense</td>
            <td class="text-right tabular-nums border-bottom">{{ money(($total_expense ?? 0) * $exchangeRate, $currencyCode, true) }}</td>
        </tr>
        <tr>
            <td></td>
            <td><strong>Balance</strong></td>
            <td class="tabular-nums text-right {{ $total_income - $total_expense > 0 ? 'is-profit' : 'is-loss' }}"><strong>{{ money(($total_income - $total_expense) * $exchangeRate, $currencyCode, true) }}</strong></td>
        </tr>
        <tr>
            <td colspan="3"><div class="sep"></div></td>
        </tr>
        <tr>
            <td colspan="3"><strong>PROFIT / LOSS</strong></td>
        </tr>
        <tr>
            <td></td>
            <td class="border-bottom">Invoice</td>
            <td class="text-right tabular-nums border-bottom">{{ money($total_invoice * $exchangeRate, $currencyCode, true) }}</td>
        </tr>
        <tr>
            <td></td>
            <td class="border-bottom">Expense</td>
            <td class="text-right tabular-nums border-bottom">{{ money(($total_expense ?? 0) * $exchangeRate, $currencyCode, true) }}</td>
        </tr>
        <tr>
            <td></td>
            <td><strong>Profit / Loss</strong></td>
            <td class="tabular-nums text-right {{ $total_invoice - $total_expense > 0 ? 'is-profit' : 'is-loss' }}"><strong>{{ money(($total_invoice - $total_expense) * $exchangeRate, $currencyCode, true) }}</strong></td>
        </tr>
    </table>

    @foreach ($cashes as $key => $value)
    @pageBreak

    <h1 class="title">{{ $key }}</h1>
    <table style="margin-top: 0.5cm">
        @php $total = 0; @endphp
        @foreach ($value as $item)
            @php
                if ($item->amount) {
                    $total += $item->amount * $item->exchange_rate * ($item->type === 'c' ? -1 : 1);
                } else {
                    $total += $item->cash_in_c - $item->cash_out_c;
                }
            @endphp
            <tr>
                <td class="border-bottom" style="width: 2.75cm">{{ $item->cashed_at->format('d/m/Y') }}</td>
                <td class="border-bottom">
                    @if ($item->category)
                        <small>{{ ($item->category->parent ? $item->category->parent->name . ' > ' : '') . $item->category->name }}</small>
                    @endif
                    <div>{{ $item->description ?? $item->details }}</div>
                </td>
                @if ($item->amount)
                    @if ($item->type === 'c')
                        <td class="text-right tabular-nums border-bottom nowrap"></td>
                        <td class="text-right tabular-nums border-bottom nowrap">{{ money($item->amount * $item->exchange_rate * -1 * $exchangeRate, $currencyCode, true) }}</td>
                    @else
                        <td class="text-right tabular-nums border-bottom nowrap">{{ money($item->amount * $item->exchange_rate * $exchangeRate, $currencyCode, true) }}</td>
                        <td class="text-right tabular-nums border-bottom nowrap"></td>
                    @endif
                @else
                    <td class="text-right tabular-nums border-bottom nowrap">{{ $item->cash_in_c ? money($item->cash_in_c * $exchangeRate, $currencyCode, true) : '' }}</td>
                    <td class="text-right tabular-nums border-bottom nowrap">{{ $item->cash_out_c ? money($item->cash_out_c * -1 * $exchangeRate, $currencyCode, true) : '' }}</td>
                @endif
            </tr>
        @endforeach
        <tr>
            <td style="width: 2.75cm"></td>
            <td>
                <strong>Balance</strong>
            </td>
            <td class="text-right tabular-nums nowrap" colspan="2">
                <strong>{{ money($total * $exchangeRate, $currencyCode, true) }}</strong>
            </td>
        </tr>
    </table>
    @endforeach
</div>
